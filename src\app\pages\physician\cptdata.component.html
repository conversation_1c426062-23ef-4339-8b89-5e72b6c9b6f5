<div class="modal fade" id="CPTDataPhy" tabindex="-1" aria-labelledby="exampleModalCenterTitle" maria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle">Assign CPT Codes</h5>
        <button type="button" class="close enhanced-close-btn" aria-label="Close" data-modal-id="CPTDataPhy">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- search start -->

        <div class="row">
          <div class="col-12">
            <!-- tabs start -->
            <nav>
              <div class="nav nav-tabs border-bottom-0" id="nav-tabpop" role="tablist">
                <a class="nav-item nav-link pop-nav px-5" id="search-tab" data-toggle="tab" href="#nav-search"
                  aria-controls="nav-profile" (click)="search()">Search</a>
                <a class="nav-item nav-link pop-nav active px-5" id="favorite-tab" data-toggle="tab" href="#nav-fav"
                  role="tab" role="tab" aria-controls="nav-home" aria-selected="true" (click)="fav()">Favorites</a>
              </div>
            </nav>
            <div class="card px-3 py-2 shadow tab-content" id="nav-tabContent">

              <div class="tab-pane fade show active" id="nav-fav" role="tabpanel" aria-labelledby="favorite-tab">
                <div class="row scrolling" style="height:300px;overflow-x:scroll;">
                  <div class="col-12">
                    <div class="input-group">
                      <input type="text" class="form-control small" maxlength="1000" placeholder="Search Here.."
                        aria-label="Search" [(ngModel)]="searchCPTs" [ngModelOptions]="{standalone: true}"
                        aria-describedby="basic-addon2">
                      <div class="input-group-append">
                        <button class="bg-gradient-info btn text-white" type="button">
                          <i class="fas fa-search fa-sm"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="col-12 my-2" *ngFor="let item of lisfOfCPTData |gridFilter:{cptname:searchCPTs}:false">
                    <ng-container *ngIf="item.status">
                      <div class="custom-control custom-checkbox">
                        <input *ngIf="item.isExist" checked type="checkbox" class="custom-control-input"
                          id="{{item.cpT_ID}}" value="{{item.cptname}}" (change)="chkChangeEvent($event)">
                        <input *ngIf="!item.isExist" type="checkbox" class="custom-control-input" id="{{item.cpT_ID}}"
                          value="{{item.cptname}}" (change)="chkChangeEvent($event)">
                        <label class="custom-control-label" for="{{item.cpT_ID}}">{{item.cptname}}</label>
                        <span class="custom-control-inline"><a style="font-size:20px;color:green;"
                            (click)="favUnfavCPTCodesAdd(false,item)">★</a></span>
                      </div>
                    </ng-container>
                  </div>

                </div>
                <div class="modal-footer py-2">
                  <button class="btn btn-outline-info float-right" data-dismiss="modal"
                    (click)="addCPTData(lisfOfCPTData)">Save</button>
                </div>
              </div>

              <div class="tab-pane fade" id="nav-search" role="tabpanel" aria-labelledby="search-tab">
                <div class="row scrolling" style="height:300px;overflow-x:scroll;">
                  <div class="col-12">
                    <div class="input-group">
                      <input type="text" class="form-control small" maxlength="1000" placeholder="Search Here.."
                        aria-label="Search" [(ngModel)]="filterCPTs" [ngModelOptions]="{standalone: true}"
                        aria-describedby="basic-addon2" (keyup)="searchCPTData()">
                      <div class="input-group-append">
                        <button class="bg-gradient-info btn text-white" type="button">
                          <i class="fas fa-search fa-sm"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="col-12 my-2" *ngFor="let item of lisfOfSearchCPTData">
                    <div class="custom-control custom-checkbox">
                      <input *ngIf="item.isExist" checked type="checkbox" class="custom-control-input"
                        id="sch-{{item.cpT_ID}}" value="{{item.cptname}}" (change)="chkChangeEventSearch($event)">
                      <input *ngIf="!item.isExist" type="checkbox" class="custom-control-input" id="sch-{{item.cpT_ID}}"
                        value="{{item.cptname}}" (change)="chkChangeEventSearch($event)">
                      <label class="custom-control-label" for="sch-{{item.cpT_ID}}">{{item.cptname}}</label>
                      <span class="custom-control-inline">
                        <a *ngIf="item.status" style="font-size:20px;color:green;"
                          (click)="favUnfavCPTCodesAdd(false,item)">★</a>
                        <a *ngIf="!item.status" style="font-size:20px;color:#777676;"
                          (click)="favUnfavCPTCodesAdd(true,item)">★</a>
                      </span>
                    </div>
                  </div>

                </div>
                <div class="modal-footer py-2">
                  <button class="btn btn-outline-info float-right" data-dismiss="modal"
                    (click)="addCPTDataSearch(lisfOfSearchCPTData)">Save</button>
                </div>
              </div>

            </div>
            <!-- tabs end -->

          </div>
        </div>

        <!-- search end -->
      </div>
    </div>
  </div>
</div>