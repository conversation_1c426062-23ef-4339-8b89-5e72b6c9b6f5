<!-- Page Wrapper -->
<div id="wrapper">

  <!-- Sidebar -->
  <ul *ngIf="userAccess.status=='SUCCESS'" class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion"
    id="accordionSidebar" [ngClass]="{'toggled': device}">
    <!-- Sidebar - Brand -->
    <a class="sidebar-brand d-flex align-items-center justify-content-center cursor-pointer"
      routerLink="dashboard/cpt-analysis" routerLinkActive="active">
      <div class="sidebar-brand-text mx-3">
        <img alt=' ' src="../assets/img/gradRoundswhite.svg" class="width-150">
      </div>
    </a>
    <!-- Divider -->
    <hr class="sidebar-divider my-0">
    <!-- Nav Item - Dashboard -->
    <li class="nav-item active"
      *ngIf="(!device && userAccess.dashboardModuleAccess=='YES')||(!device && userAccess.kareoDashboardModuleAccess=='YES')">
      <a class="nav-link cursor-pointer {{routeName=='dashboardTab'?'table-active':''}}"
        routerLink="dashboard/cpt-analysis" routerLinkActive="active">
        <i class="fas fa-fw fa-tachometer-alt"></i>
        <span>Dashboard</span>
      </a>
    </li>

    <!-- Divider -->

    <hr class="sidebar-divider">
    <!-- Nav Item - Pages Collapse Menu -->
    <li class="nav-item" *ngIf="(userAccess.physicianModuleAccess=='YES')||(userAccess.residentModuleAccess=='YES')">
      <a id="ankphysicianTab" class="nav-link cursor-pointer {{routeName=='physicianTab'?'table-active collapsed':''}}"
        data-toggle="collapse" data-target="#physicianTab" aria-expanded="true" aria-controls="physicianTab"
        (click)="addTogglesForChilds('#physicianTab')">
        <img alt=' ' src="../assets/img/doctor.svg" class="width-20">
        <span>Physician</span>
      </a>
      <div id="physicianTab" class="collapse {{routeName=='physicianTab'?'show':''}}" aria-labelledby="headingTwo"
        data-parent="#accordionSidebar">
        <div class="bg-white py-2 collapse-inner rounded">
          <a class="collapse-item cursor-pointer {{pageName=='My Patients'?'active':''}}"
            routerLink="physician/my-patients">My Patients</a>
          <a class="collapse-item cursor-pointer {{pageName=='My Group Patients'?'active':''}}"
            routerLink="physician/my-group-patients">My Group Patients</a>
          <a class="collapse-item cursor-pointer {{pageName=='Hospital Census'?'active':''}}"
            routerLink="physician/hospital-census">Hospital Census</a>
          <a class="collapse-item cursor-pointer {{pageName=='Discharge Patients'?'active':''}}"
            routerLink="physician/discharge-patients">Discharge Patients</a>
          <a class="collapse-item cursor-pointer {{pageName=='Pending Approval Encounters'?'active':''}}"
            routerLink="physician/pending-approval-encounters">Pending Approval Encounters</a>
          <a class="collapse-item cursor-pointer {{pageName=='Physician View/Edit'?'active':''}}" *ngIf="!device"
            routerLink="physician/view-modify-encounters">View/Modify Encounters</a>
          <a *ngIf='userAccess.physicianNonPrimeFacilityAccess'
            class="collapse-item cursor-pointer {{(pageName=='Add Patient' && routeName=='physicianTab')?'active':''}}"
            routerLink="physician/add-patient"
            [state]="{title:'Add Patient',tabName:'physicianTab',userType:'PHYSICIAN'}">Add Patient</a>
          <a class="collapse-item cursor-pointer {{pageName=='Unbilled Encounters'?'active':''}}"
            routerLink="physician/unbilled-encounters">Unbilled Encounters</a>
        </div>
      </div>
    </li>

    <li class="nav-item" *ngIf="!device && userAccess.coordinatorModuleAccess=='YES'">
      <a id="ankcoordinatorTab"
        class="nav-link cursor-pointer {{routeName=='coordinatorTab'?'table-active collapsed':''}}"
        data-toggle="collapse" data-target="#coordinatorTab" aria-expanded="true" aria-controls="coordinatorTab"
        (click)="addTogglesForChilds('#coordinatorTab')">
        <img alt=' ' src="../assets/img/Coordinater.svg" class="width-20">
        <span>Coordinator</span>
      </a>
      <div id="coordinatorTab" class="collapse {{routeName=='coordinatorTab'?'show':''}}" aria-labelledby="headingPages"
        data-parent="#accordionSidebar">
        <div class="bg-white py-2 collapse-inner rounded">
          <a class="collapse-item cursor-pointer {{pageName=='Coordinator Overview'?'active':''}}"
            routerLink="coordinator/search">Coordinator</a>
          <a class="collapse-item cursor-pointer {{pageName=='Coordinator View/Edit'?'active':''}}"
            routerLink="coordinator/coordinator-edit">View/Modify Encounters</a>
          <a *ngIf='userAccess.coordinatorNonPrimeFacilityAccess'
            class="collapse-item cursor-pointer {{(pageName=='Add Patient' && routeName=='coordinatorTab')?'active':''}}"
            routerLink="coordinator/add-patient"
            [state]="{title:'Add Patient',tabName:'coordinatorTab',userType:'COORDINATOR'}">Add Patient</a>
          <a class="collapse-item cursor-pointer {{(pageName=='Submit Missing Encounter' && routeName=='coordinatorTab')?'active':''}}"
            routerLink="coordinator/submit-missing-encounter"
            [state]="{title:'Submit Missing Encounter',tabName:'coordinatorTab', userType:'COORDINATOR'}">Submit Missing
            Encounter</a>
          <a class="collapse-item cursor-pointer {{pageName=='Undo Discharge Patient'?'active':''}}"
            routerLink="coordinator/undo-discharge-patient">Undo Discharge Patient</a>
          <a class="collapse-item cursor-pointer {{pageName=='Assignments History'?'active':''}}"
            routerLink="coordinator/assignments-history">Assignments History</a>
           <!-- <a class="collapse-item cursor-pointer {{pageName=='Audit View'?'active':''}}"
            routerLink="coordinator/audit-view">Audit View</a> -->

        </div>
      </div>
    </li>
    <li class="nav-item" *ngIf="!device && userAccess.auditorModuleAccess=='YES'">
      <a class="nav-link cursor-pointer {{pageName=='Auditor View/Modify'?'table-active':''}}"
        routerLink="auditor/auditor-edit" routerLinkActive="active" (click)="checkURL()">
        <img alt=' ' src="../assets/img/audit.svg" class="width-20">
        <span>Auditor</span>
      </a>
    </li>

    <!-- Nav Item - Charts -->
    <li class="nav-item" *ngIf="!device && userAccess.billerModuleAccess=='YES'">
      <a id="ankBillingTab" class="nav-link cursor-pointer {{routeName=='billingTab'?'table-active collapsed':''}}"
        data-toggle="collapse" data-target="#billingTab" aria-expanded="true" aria-controls="billingTab"
        (click)="addTogglesForChilds('#billingTab')">
        <img alt=' ' src="../assets/img/Billing.svg" class="width-20">
        <span>Billing</span>
      </a>
      <div id="billingTab" class="collapse {{routeName=='billingTab'?'show':''}}" aria-labelledby="headingPages"
        data-parent="#accordionSidebar">
        <div class="bg-white py-2 collapse-inner rounded">
          <a class="collapse-item cursor-pointer {{pageName=='Billing Overview'?'active':''}}"
            routerLink="biller/index">Billing</a>
          <a class="collapse-item cursor-pointer {{(pageName=='Submit Missing Encounter' && routeName=='billingTab')?'active':''}}"
            routerLink="biller/submit-missing-encounter"
            [state]="{title:'Submit Missing Encounter',tabName:'billingTab', userType:'BILLER'}">Submit Missing
            Encounter</a>
          <a *ngIf='userAccess.billerNonPrimeFacilityAccess'
            class="collapse-item cursor-pointer {{(pageName=='Add Patient' && routeName=='billingTab')?'active':''}}"
            routerLink="biller/add-patient" [state]="{title:'Add Patient',tabName:'billingTab',userType:'BILLER'}">Add
            Patient</a>
        </div>
      </div>
    </li>

    <!-- Nav Item - Tables -->
    <li class="nav-item" *ngIf="!device && userAccess.reportModuleAccess=='YES'">
      <a class="nav-link cursor-pointer {{routeName=='reportsTab'?'table-active collapsed':''}}" data-toggle="collapse"
        data-target="#reportsTab" aria-expanded="true" aria-controls="reportsTab"
        (click)="addTogglesForChilds('#reportsTab')">
        <img alt=' ' src="../assets/img/Reports.svg" class="width-20">
        <span>Reports</span>
      </a>
      <div id="reportsTab" class="collapse {{routeName=='reportsTab'?'show':''}}" aria-labelledby="headingPages"
        data-parent="#accordionSidebar">
        <div class="bg-white py-2 collapse-inner rounded">
          <a class="collapse-item cursor-pointer" routerLink="report/encounter-report"
            routerLinkActive="active">Encounter Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/deleted-encounter-report"
            routerLinkActive="active">Deleted Encounter Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/cpt-report" routerLinkActive="active">CPT
            Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/missing-encounter-report"
            routerLinkActive="active">Missing Encounter Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/alerts-report" routerLinkActive="active">Alert's
            Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/cpt-summary-btween-years"
            routerLinkActive="active">CPT Summary Between Years</a>
          <a class="collapse-item cursor-pointer" routerLink="report/cpt-summary" routerLinkActive="active">CPT
            Summary</a>
          <a class="collapse-item cursor-pointer" routerLink="report/billed-encounter-report"
            routerLinkActive="active">Kareo Encounters Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/notes-report" routerLinkActive="active">Notes
            Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/view-facilities" routerLinkActive="active">View
            Facilities</a>
          <a class="collapse-item cursor-pointer" routerLink="report/mismatch-encounter-report"
            routerLinkActive="active">Mismatch Encounter Report</a>
          <a class="collapse-item cursor-pointer" routerLink="report/next-gen-encounter-report"
            routerLinkActive="active">Nextgen Encounter Report</a>
        </div>
      </div>
    </li>
    <li class="nav-item">
      <a class="nav-link cursor-pointer {{routeName=='messageHubTab'?'table-active':''}}"
        routerLink="message-hub/message-hub" routerLinkActive="active" (click)="checkURL()">
        <img alt=' ' src="../assets/img/messageHub.svg" class="width-20">
        <span>Message Hub</span>
      </a>
    </li>
    <li class="nav-item"
      *ngIf="(!device && userAccess.physicianModuleAccess=='YES')||(!device && userAccess.residentModuleAccess=='YES')||(!device && userAccess.auditorModuleAccess=='YES')||(!device && userAccess.coordinatorModuleAccess=='YES')||(!device && userAccess.billerModuleAccess=='YES')||(!device && userAccess.reportModuleAccess=='YES')">
      <a class="nav-link cursor-pointer {{routeName=='AttachmentsTab'?'table-active':''}}" routerLink="attachments"
        routerLinkActive="active" (click)="checkURL()">
        <img alt=' ' src="../assets/img/attached.svg" class="width-20">
        <span>Attachments</span>
      </a>
    </li>
    <li class="nav-item"
      *ngIf="(!device && userAccess.physicianModuleAccess=='YES')||(!device && userAccess.residentModuleAccess=='YES')||(!device && userAccess.auditorModuleAccess=='YES')||(!device && userAccess.coordinatorModuleAccess=='YES')||(!device && userAccess.billerModuleAccess=='YES')||(!device && userAccess.reportModuleAccess=='YES')">
      <a class="nav-link cursor-pointer {{routeName=='HistoricalEncountersTab'?'table-active':''}}"
        routerLink="historical-encounters" routerLinkActive="active" (click)="checkURL()">
        <img alt=' ' src="../assets/img/Reports.svg" class="width-20">
        <span>Historical Encounters</span>
      </a>
    </li>
    <li class="nav-item" *ngIf="(!device && userAccess.userManagementAccess=='YES')||(!device && userAccess.adminModuleAccess=='YES')">
      <a class="nav-link cursor-pointer {{routeName=='usermanagementTab'?'table-active collapsed':''}}"
        data-toggle="collapse" data-target="#usermanagementTab" aria-expanded="true" aria-controls="usermanagementTab"
        (click)="addTogglesForChilds('#usermanagementTab')">
        <img alt=' ' src="../assets/img/Reports.svg" class="width-20">
        <span>User Management</span>
      </a>
      <div id="usermanagementTab" class="collapse" aria-labelledby="headingPages" data-parent="#accordionSidebar">
        <div class="bg-white py-2 collapse-inner rounded">
          <a class="collapse-item cursor-pointer {{pageName=='Add/Modify User'?'active':''}}"
            routerLink="usermanagement/add-modify-user">Add/Modify Users</a>
          <a *ngIf="userAccess.adminModuleAccess=='YES'"
            class="collapse-item cursor-pointer {{pageName=='Add/Modify Facility'?'active':''}}"
            routerLink="usermanagement/add-modify-facility">Add/Modify Facility</a>
          <a class="collapse-item cursor-pointer {{pageName=='Add/Modify Groups'?'active':''}}"
            routerLink="usermanagement/add-modify-groups">Add/Modify Groups</a>

        </div>
      </div>
    </li>

    <!-- Divider -->
    <hr class="sidebar-divider d-none d-md-block">

    <!-- Sidebar Toggler (Sidebar) -->
    <div class="text-center d-none d-md-inline">
      <button class="rounded-circle border-0" id="sidebarToggle" (click)="sidebarToggle()"></button>
    </div>
  </ul>
  <!-- End of Sidebar -->
  <!-- Content Wrapper -->
  <div id="content-wrapper" class="d-flex flex-column">

    <!-- Main Content Starts -->
    <div id="content">
      <nav *ngIf="userAccess.status=='SUCCESS'" class="navbar navbar-expand  static-top topbar">

        <!-- Sidebar Toggle (Topbar) -->
        <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3" (click)="sidebarToggle()">
          <i class="fa fa-bars"></i>
        </button>
        <h2 class="font-weight-bold mb-0 ml-auto text-primary">{{pageName}}</h2>
        <ul class="navbar-nav ml-auto">
          <!-- Nav Item - User Information -->
          <li class="nav-item dropdown no-arrow">
            <a class="nav-link dropdown-toggle cursor-pointer" id="userDropdown" data-toggle="dropdown"
              aria-haspopup="true" aria-expanded="false">
              <span class="mr-2 d-none d-lg-inline text-gray-600 small">{{userName}}</span>
              <img alt=' ' class="img-profile rounded-circle" src="../../../assets/img/avatar.png">
            </a>
            <!-- Dropdown - User Information -->
            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in" aria-labelledby="userDropdown">
              <a class="dropdown-item cursor-pointer">
                <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i> Profile
              </a>

              <div class="dropdown-divider"></div>
              <a class="dropdown-item cursor-pointer" data-toggle="modal" data-target="#logoutModal" (click)="logout()">
                <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i> Logout
              </a>
            </div>
          </li>

        </ul>
      </nav>
      <div id="loading"></div>
      <router-outlet *ngIf="!isIframe" (activate)="onActivate($event)"></router-outlet>

    </div>
    <!-- Main Content Ends -->
    <!-- Footer -->
    <footer class="sticky-footer bg-white">
      <div class="container my-auto">
        <div class="copyright text-center my-auto">
          <span>Copyright &copy; Prime Grand Rounds {{year}}</span>
        </div>
      </div>
    </footer>
    <!-- End of Footer -->
  </div>
  <!-- End of Content Wrapper -->
</div>
<!-- End of Page Wrapper -->
<!-- Scroll to Top Button-->
<a class="scroll-to-top rounded" href="#page-top">
  <i class="fas fa-angle-up"></i>
</a>

<!-- Session Expiration Popup Starts -->
<div id="sessExpModel" class="modal fade">
  <div class="modal-dialog custom-modal modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title">Session Expiration Alert!</h5>
      </div>
      <div class="modal-body">
        <p>Your login session has expired and logging out of the application {{userIdleTimeStarted-userIdleTime}}</p>
      </div>
      <div class="modal-footer py-2">
        <button class="btn btn-outline-info float-right enhanced-close-btn" (click)="logout()" data-modal-id="sessExpModel">Ok</button>
        <button class="btn btn-outline-info float-right enhanced-close-btn" (click)="stopLogout()" data-modal-id="sessExpModel">Stop</button>
      </div>
    </div>
  </div>
</div>
<!-- Session Expiration Popup Ends -->

<!-- Mobile Session Expiration Popup Starts -->
<div id="mobileSessExpModel" class="modal fade">
  <div class="modal-dialog custom-modal modal-dialog-centered mobile-session-modal">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title">Session Expiration Alert!</h5>
      </div>
      <div class="modal-body">
        <p>Your login session has expired and logging out of the application</p>
        <p class="countdown-timer">{{userIdleTime}}</p>
      </div>
      <div class="modal-footer py-2">
        <button class="btn btn-outline-info float-right enhanced-close-btn" (click)="logout()" data-modal-id="mobileSessExpModel">Ok</button>
        <button class="btn btn-outline-info float-right enhanced-close-btn" (click)="stopMobileLogout()" data-modal-id="mobileSessExpModel">Stop</button>
      </div>
    </div>
  </div>
</div>
<!-- Mobile Session Expiration Popup Ends -->
<!-- Missing ENcounter popup -->
<div class="modal fade" id="submittedAll" tabindex="-1" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-sm">
    <div class="modal-content">

      <div class="modal-body p-0">
        <div class="mx-0 row text-center">
          <div class="col-12 py-3">
            <img alt=' ' src="../assets/img/checked.svg" width="50px">
          </div>
          <div class="col-12 ">
            <h6>Submitted Successfully...</h6>
          </div>
          <div class="col-12 text-center">
            <button class="btn btn-style btn-primary my-3 px-4 enhanced-close-btn" data-modal-id="successModel">Ok</button>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
<!-- Missing Encounter end -->

<div id="newVersionModel" class="modal fade">
  <div class="modal-dialog custom-modal modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title">Version Update</h5>
      </div>
      <div class="modal-body">
        <p>A new version is available. Please click on Reload to start using new version.</p>
      </div>
      <div class="modal-footer py-2">
        <button class="btn btn-outline-info float-right" aria-label="Refresh" (click)="applyUpdate()">Reload</button>
      </div>
    </div>
  </div>
</div>
