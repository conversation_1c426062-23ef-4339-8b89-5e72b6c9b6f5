<div class="modal fade" id="CPTData" tabindex="-1" aria-labelledby="exampleModalCenterTitle" maria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" [ngClass]="{'mobile-cpt-modal': device}">
    <!-- Added mobile-specific class -->
    <div class="modal-content">
      <div class="modal-header py-2">
        <h5 class="modal-title" id="exampleModalCenterTitle">{{cptType}} CPT Codes</h5>
        <button type="button" class="close enhanced-close-btn" aria-label="Close" data-modal-id="CPTData">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- search start -->

        <div class="row">
          <div class="col-12">
            <!-- tabs start -->
            <nav>
              <div class="nav nav-tabs border-bottom-0" id="nav-tabpop" role="tablist">
                <a class="nav-item nav-link pop-nav px-5" id="search-tab" data-toggle="tab" href="#nav-search"
                  aria-controls="nav-profile" (click)="search()">Search</a>
                <a class="nav-item nav-link pop-nav active px-5" id="favorite-tab" data-toggle="tab" href="#nav-fav"
                  role="tab" role="tab" aria-controls="nav-home" (click)="fav()">Favorites</a>
              </div>
            </nav>
            <div class="card px-3 py-2 shadow tab-content" id="nav-tabContent">

              <div class="tab-pane fade show active" id="nav-fav" role="tabpanel" aria-labelledby="favorite-tab">
                <div class="row scrolling" style="height:300px;overflow-x:scroll;">
                  <div class="col-12">
                    <div class="input-group">
                      <input type="text" class="form-control small" maxlength="1000" placeholder="Search Here.."
                        aria-label="Search" [(ngModel)]="searchCPTs" [ngModelOptions]="{standalone: true}"
                        aria-describedby="basic-addon2">
                      <div class="input-group-append">
                        <button class="bg-gradient-info btn text-white" type="button">
                          <i class="fas fa-search fa-sm"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="col-12 my-2" *ngFor="let item of lisfOfCPTData |gridFilter:{cptname:searchCPTs}:false">
                    <ng-container *ngIf="item.status">
                      <div class="custom-control custom-checkbox" *ngIf="cptType=='Add'">
                        <input *ngIf="item.isExist" checked type="checkbox" class="custom-control-input"
                          id="{{item.cpT_ID}}" value="{{item.cptname}}"
                          (change)="chkChangeEventAdd(item.cptname, $event)">
                        <input *ngIf="!item.isExist" type="checkbox" class="custom-control-input" id="{{item.cpT_ID}}"
                          value="{{item.cptname}}" (change)="chkChangeEventAdd(item.cptname, $event)">
                        <label class="custom-control-label" for="{{item.cpT_ID}}">{{item.cptname}}</label>
                        <span class="custom-control-inline"><a style="font-size:20px;color:green;"
                            (click)="favUnfavCPTCodesAdd(encounterObj.physicianmailid,false,item)">★</a></span>
                        <div class="d-inline-block ml-2" *ngIf="item.cpt_count>0">
                          <div class="quantity">
                            <a class="quantity__minus" (click)="deleteCPTCode(encounterObj,item)"><span>-</span></a>
                            <input name="quantity" type="text" class="quantity__input" value="{{item.cpt_count}}"
                              disabled>
                            <a class="quantity__plus" (click)="addCPTCode(encounterObj,item)"><span>+</span></a>
                          </div>
                        </div>
                      </div>
                      <div class="col-12 my-2" *ngIf="item.isRemoved && item.isOld">
                        <input name="quantity" maxlength="1000" placeholder="Remove Reason .." type="text"
                          [(ngModel)]="item.deletedReason" class="form-control small m-2"
                          [ngClass]="{ 'is-invalid': submitted && !item.haveDeleteReason }"
                          [ngModelOptions]="{standalone: true}">
                      </div>


                      <div *ngIf="cptType!='Add'" class="radio-color-div">
                        <input *ngIf="cptType.includes(item.cptname)" name="cptCode" checked type="radio"
                          id="{{item.cpT_ID}}" value="{{item.cptname}}" (change)="chkChangeEventUpdate(item.cptname)">
                        <input *ngIf="!cptType.includes(item.cptname)" name="cptCode" type="radio" id="{{item.cpT_ID}}"
                          value="{{item.cptname}}" (change)="chkChangeEventUpdate(item.cptname)">{{item.cptname}}
                        <a style="font-size:20px;color:green;"
                          (click)="favUnfavCPTCodesAdd(encounterObj.physicianmailid,false,item)">★</a>
                      </div>
                    </ng-container>
                  </div>

                </div>
                <div class="alert alert-danger" *ngIf="submitted && !haveDeleteReason">
                  <strong>Error:</strong> Remove reasons are required.
                </div>
                <div class="modal-footer py-2">
                  <button *ngIf="cptType=='Add'" class="btn btn-outline-info float-right"
                    (click)="addCPTDataFvSr(encounterObj,lisfOfCPTData)">Save</button>
                  <button *ngIf="cptType!='Add'" class="btn btn-outline-info float-right"
                    (click)="editCPTData(encounterObj,cptType)">Save</button>
                </div>
              </div>

              <div class="tab-pane fade" id="nav-search" role="tabpanel" aria-labelledby="search-tab">
                <div class="row scrolling" style="height:300px;overflow-x:scroll;">
                  <div class="col-12">
                    <div class="input-group">
                      <input type="text" class="form-control small" maxlength="1000" placeholder="Search Here.."
                        aria-label="Search" [(ngModel)]="filterCPTs" [ngModelOptions]="{standalone: true}"
                        aria-describedby="basic-addon2" (keyup)="searchCPTData(encounterObj)">
                      <div class="input-group-append">
                        <button class="bg-gradient-info btn text-white" type="button">
                          <i class="fas fa-search fa-sm"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="col-12 my-2"
                    *ngFor="let item of lisfOfSearchCPTData |gridFilter:{cptname:filterCPTs}:false">
                    <div class="custom-control custom-checkbox" *ngIf="cptType=='Add'">
                      <input *ngIf="item.isExist" checked type="checkbox" class="custom-control-input"
                        id="sch-{{item.cpT_ID}}" value="{{item.cptname}}"
                        (change)="chkChangeEventSearchAdd(item.cptname, $event)">
                      <input *ngIf="!item.isExist" type="checkbox" class="custom-control-input" id="sch-{{item.cpT_ID}}"
                        value="{{item.cptname}}" (change)="chkChangeEventSearchAdd(item.cptname, $event)">
                      <label class="custom-control-label" for="sch-{{item.cpT_ID}}">{{item.cptname}}</label>
                      <span class="custom-control-inline">
                        <a *ngIf="item.status" style="font-size:20px;color:green;"
                          (click)="favUnfavCPTCodesAdd(encounterObj.physicianmailid,false,item)">★</a>
                        <a *ngIf="!item.status" style="font-size:20px;color:#777676;"
                          (click)="favUnfavCPTCodesAdd(encounterObj.physicianmailid,true,item)">★</a>
                      </span>
                      <div class="d-inline-block ml-2" *ngIf="item.cpt_count>0">
                        <div class="quantity">
                          <a class="quantity__minus" (click)="deleteCPTCode(encounterObj,item)"><span>-</span></a>
                          <input name="quantity" type="text" class="quantity__input" value="{{item.cpt_count}}"
                            disabled>
                          <a class="quantity__plus" (click)="addCPTCode(encounterObj,item)"><span>+</span></a>
                        </div>
                      </div>
                    </div>
                    <div class="col-12 my-2" *ngIf="item.isRemoved && item.isOld">
                      <input name="quantity" maxlength="1000" placeholder="Remove Reason .." type="text"
                        [(ngModel)]="item.deletedReason" class="form-control small m-2"
                        [ngClass]="{ 'is-invalid': submitted && !item.haveDeleteReason }"
                        [ngModelOptions]="{standalone: true}">
                    </div>
                    <div *ngIf="cptType!='Add'" class="radio-color-div">
                      <input *ngIf="cptType.includes(item.cptname)" name="cptCode" checked type="radio"
                        id="sch-{{item.cpT_ID}}" value="{{item.cptname}}" (change)="chkChangeEventUpdate(item.cptname)">
                      <input *ngIf="!cptType.includes(item.cptname)" name="cptCode" type="radio"
                        id="sch-{{item.cpT_ID}}" value="{{item.cptname}}" (change)="chkChangeEventUpdate(item.cptname)">
                      {{item.cptname}}
                      <a *ngIf="item.status" style="font-size:20px;color:green;"
                        (click)="favUnfavCPTCodesAdd(encounterObj.physicianmailid,false,item)">★</a>
                      <a *ngIf="!item.status" style="font-size:20px;color:#777676;"
                        (click)="favUnfavCPTCodesAdd(encounterObj.physicianmailid,true,item)">★</a>
                    </div>
                  </div>

                </div>
                <div class="alert alert-danger" *ngIf="submitted && !haveDeleteReason">
                  <strong>Error:</strong> Remove reasons are required.
                </div>
                <div class="modal-footer py-2">
                  <button *ngIf="cptType=='Add'" class="btn btn-outline-info float-right"
                    (click)="addCPTDataFvSr(encounterObj,lisfOfSearchCPTData)">Save</button>
                  <button *ngIf="cptType!='Add'" class="btn btn-outline-info float-right"
                    (click)="editCPTDataSearch(encounterObj,cptType)">Save</button>
                </div>
              </div>

            </div>
            <!-- tabs end -->

          </div>
        </div>

        <!-- search end -->
      </div>
    </div>
  </div>
</div>