import { Injectable } from '@angular/core';
declare let $: any;

@Injectable({
  providedIn: 'root'
})
export class ModalService {

  constructor() { }

  /**
   * Enhanced modal close handler with proper cleanup for iPhone and Safari
   * Replicates the working pattern from CPT popup components
   */
  closeModal(modalId: string, callback?: () => void): void {
    // Ensure modalId starts with #
    const fullModalId = modalId.startsWith('#') ? modalId : '#' + modalId;
    
    // Explicit modal hide with proper cleanup - same pattern as working modals
    $(fullModalId).modal('hide');

    // Additional cleanup for iPhone and Safari browsers
    if (this.isIPhoneOrSafari()) {
      this.performIPhoneSafariCleanup();
    }

    // Execute callback if provided
    if (callback) {
      setTimeout(() => {
        callback();
      }, 350); // Wait for modal animation to complete
    }
  }

  /**
   * Enhanced modal open handler with proper setup for iPhone and Safari
   */
  openModal(modalId: string, callback?: () => void): void {
    // Ensure modalId starts with #
    const fullModalId = modalId.startsWith('#') ? modalId : '#' + modalId;
    
    // Pre-cleanup for iPhone and Safari to prevent stuck modals
    if (this.isIPhoneOrSafari()) {
      this.performIPhoneSafariCleanup();
    }

    // Open the modal
    $(fullModalId).modal('show');

    // Execute callback if provided
    if (callback) {
      setTimeout(() => {
        callback();
      }, 350); // Wait for modal animation to complete
    }
  }

  /**
   * Force close all modals - emergency cleanup for iPhone/Safari
   */
  forceCloseAllModals(): void {
    $('.modal').modal('hide');
    
    if (this.isIPhoneOrSafari()) {
      setTimeout(() => {
        this.performIPhoneSafariCleanup();
      }, 100);
    }
  }

  /**
   * Setup modal event handlers for proper cleanup
   * Call this in component ngOnInit or ngAfterViewInit
   */
  setupModalEventHandlers(modalSelector: string = '.modal'): void {
    // Handle modal hidden event for cleanup
    $(document).off('hidden.bs.modal', modalSelector);
    $(document).on('hidden.bs.modal', modalSelector, () => {
      // Additional cleanup for iPhone and Safari browsers
      if (this.isIPhoneOrSafari()) {
        this.performIPhoneSafariCleanup();
      }
    });

    // Handle modal show event for iPhone/Safari preparation
    $(document).off('show.bs.modal', modalSelector);
    $(document).on('show.bs.modal', modalSelector, () => {
      // Pre-cleanup for iPhone and Safari
      if (this.isIPhoneOrSafari()) {
        // Remove any existing backdrops before showing new modal
        $('.modal-backdrop').remove();
      }
    });
  }

  /**
   * Check if the current browser is iPhone or Safari
   */
  private isIPhoneOrSafari(): boolean {
    return /iPhone|iPad|iPod|Safari/i.test(navigator.userAgent);
  }

  /**
   * Perform comprehensive cleanup for iPhone and Safari browsers
   * This replicates the working pattern from successful modal implementations
   */
  private performIPhoneSafariCleanup(): void {
    // Force remove modal backdrop and restore body scroll
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('body').css('overflow', '');
    $('body').css('padding-right', '');
    
    // Additional iPhone specific fixes
    $('body').css('position', '');
    $('body').css('width', '');
    
    // Reset aria-hidden attributes properly
    $('.modal').attr('aria-hidden', 'true');
    
    // Ensure focus is returned to body
    if (document.activeElement && document.activeElement !== document.body) {
      (document.activeElement as HTMLElement).blur();
    }
    
    // Force repaint for Safari
    document.body.style.display = 'none';
    document.body.offsetHeight; // Trigger reflow
    document.body.style.display = '';
  }

  /**
   * Enhanced close button click handler
   * Use this for all modal close buttons
   */
  handleCloseButtonClick(modalId: string, event?: Event, callback?: () => void): void {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    this.closeModal(modalId, callback);
  }

  /**
   * Setup close button event listeners for a specific modal
   * Call this after modal content is loaded
   */
  setupCloseButtonHandlers(modalId: string, callback?: () => void): void {
    const fullModalId = modalId.startsWith('#') ? modalId : '#' + modalId;
    
    // Handle close button clicks
    $(fullModalId).off('click', '.close, [data-dismiss="modal"]');
    $(fullModalId).on('click', '.close, [data-dismiss="modal"]', (event: any) => {
      this.handleCloseButtonClick(modalId, event, callback);
    });
    
    // Handle escape key
    $(fullModalId).off('keydown.modal-close');
    $(fullModalId).on('keydown.modal-close', (event: any) => {
      if (event.keyCode === 27) { // Escape key
        this.handleCloseButtonClick(modalId, event, callback);
      }
    });
  }
}
