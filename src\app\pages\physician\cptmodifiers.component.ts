import { Component, Input, OnInit, AfterViewInit } from '@angular/core';
import { CommonService } from 'src/app/services/common/common.service';
import { ModalService } from 'src/app/services/common/modal.service';
import { StartNewEncounterComponent } from './start-new-encounter.component';
declare let $: any;

@Component({
  selector: 'app-cptmodifiers',
  templateUrl: './cptmodifiers.component.html',
  styles: []
})
export class CptModifiersComponent implements OnInit, AfterViewInit {
  @Input() listOfModifier: Array<any> = [];
  @Input() cptCode: string = "";
  public searchModfier: string;

  constructor(
    private readonly commonServ: CommonService,
    private readonly modalService: ModalService,
    private readonly startnewEnvCmp: StartNewEncounterComponent
  ) { }

  ngOnInit() {
    // Setup modal event handlers using the new modal service
    this.modalService.setupModalEventHandlers('#ModifierDataPhy');
  }

  ngAfterViewInit() {
    // Setup close button handlers for the modifier modal
    this.modalService.setupCloseButtonHandlers('ModifierDataPhy');
  }

  chkChangeEvent(val, event) {
    for (let item of this.listOfModifier) {
      if (item.modifierS_ID == event.target.id) {
        item.isExist = event.target.checked;
      }
    }
  }

  addModifiers(cptCode) {
    this.commonServ.startLoading();
    let modiArray = "";
    let modifiers_ids = "";
    this.listOfModifier.forEach(x => {
      if (x.isExist) {
        modiArray = modiArray + ',' + x.modifiersname.split('-')[0];
        modifiers_ids = modifiers_ids + ',' + x.modifierS_ID;
      }
    });
    this.startnewEnvCmp.listOfSelectedModifier = this.startnewEnvCmp.listOfSelectedModifier.filter(x => x.cptCode != cptCode.split('(--,')[0]);
    const index: number = this.startnewEnvCmp.testData.listofTestCPTS.indexOf(cptCode);
    if (index !== -1 && modiArray) {
      this.startnewEnvCmp.testData.listofTestCPTS[index] = cptCode.split('(--,')[0] + '(--' + modiArray + ')';
    } else if (index !== -1) {
      this.startnewEnvCmp.testData.listofTestCPTS[index] = cptCode.split('(--,')[0];
    }

    // Close modal using enhanced modal service
    this.modalService.closeModal('ModifierDataPhy');
    let item = {
      cptCode: this.startnewEnvCmp.testData.listofTestCPTS[index],
      modifiers_ids: modifiers_ids
    };
    this.startnewEnvCmp.listOfSelectedModifier.push(item);
    this.commonServ.stopLoading();
  }

}
