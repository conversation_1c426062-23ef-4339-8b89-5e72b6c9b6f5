<!-- Begin Page Content Starts -->
<div class="container-fluid">
  <!-- Content Row -->
  <div class="row">
    <!-- Area Chart -->
    <div class="col px-0">
      <div class="card shadow border-blue">
        <!-- Card Header - Dropdown -->
        <div class="card-header-new p-1" style="background: #0169ab;">
          <div class="align-items-center row mx-auto">
            <div class="col-12 px-0">
              <div class="row mt-1 mx-auto">
                <div class="col-12 col-md-2 px-1 py-1">
                  <button class="btn btn-outline-info px-2 btn-block" type="button" (click)="markAllAsRead()"> Mark All As Read </button>
                </div>
                <div class="col-12 col-md-2 px-1 py-1">
                  <button class="btn btn-outline-info px-2 btn-block" type="button" title="Send Message" (click)="openNotes()"> Send Message </button>
                </div>
                <div class="col-12 col-md-8 px-1 py-1">
                  <div class="input-group">
                    <input type="text" class="form-control small" maxlength="1000" placeholder="Search for..." aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="searchByName" [ngModelOptions]="{ standalone: true }" />
                    <div class="input-group-append">
                      <button class="bg-gradient-light btn text-dark" type="button">
                        <i class="fas fa-search fa-sm"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Card Body -->
        <div class="card-body p-1">
          <div class="row mx-0">
            <div class="col-12 px-0">
              <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover my-auto">
                  <thead>
                    <tr>
                      <th style="width:25%">From</th>
                      <th style="width:60%">Message</th>
                      <th style="width:5%">Action</th>
                      <th style="width:10%">Received Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="
                        let item of
                          listOfMessages
                            | gridFilter: {
                                displayName: searchByName,
                                contacts: searchByName,
                                message: searchByName,
                                deliveredTimeStamp: searchByName
                              }: false
                            | paginate: { itemsPerPage: 25, currentPage: p }
                      ">
                      <td style="width:25%"> From : {{ item.displayName }} To: {{ item.contacts }}
                      </td>
                      <td style="width:60%">
                        <div *ngIf="!item.isRead" style="left:27%;position:absolute;">
                          <span class="badge">New</span>
                        </div>
                        <div style="color:#4e73df;height:50px !important;overflow:hidden;padding-left:40px;">
                          <a class="cursor-pointer" (click)="markAsRead(item.groupId, item.isRead)" data-toggle="modal" data-target="#readMsg-{{ item.groupId }}">
                            {{ item.message }}
                          </a>
                        </div>
                      </td>
                      <td style="width:5%">
                        <a class="cursor-pointer" (click)="archiveMessage(item.groupId)" style="color: red;"> Archive </a>
                      </td>
                      <td style="width:10%">{{ item.dateStatus }}</td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="10" class="m-0 p-0" style="background: white !important;">
                        <pagination-controls previousLabel="" nextLabel="" (pageChange)="p = $event"></pagination-controls>
                      </td>
                    </tr>
                  </tfoot>
                </table>
                <!-- Modals for each visible message -->
                <ng-container *ngFor="
                    let item of
                      listOfMessages
                        | gridFilter: {
                            displayName: searchByName,
                            contacts: searchByName,
                            message: searchByName,
                            deliveredTimeStamp: searchByName
                          }: false
                        | paginate: { itemsPerPage: 25, currentPage: p }
                  ">
                  <div class="modal fade" id="readMsg-{{ item.groupId }}" tabindex="-1" role="dialog" [attr.aria-labelledby]="'messageModal-' + item.groupId">
                    <div class="modal-dialog modal-dialog-centered modal-lg" [ngClass]="{ 'mobile-message-modal': device }">
                      <div class="modal-content">
                        <div class="modal-header" style="background:#0169ab;color:#fff;padding:10px;">
                          <div class="float-left">
                            <h5 class="modal-title" id="messageModal-{{ item.groupId }}">{{ item.displayName }}</h5>
                          </div>
                          <div class="float-right">
                            <span class="mr-3">{{ item.dateStatus }}</span>
                            <button type="button" class="close" aria-label="Close" (click)="handleModalClose(item.groupId, item.isRead)" [attr.data-modal-id]="'readMsg-' + item.groupId">
                              <span aria-hidden="true">&times;</span>
                            </button>
                          </div>
                        </div>
                        <div class="modal-body">
                          <div class="panel panel-info">
                            <div class="panel-body p-3" style="min-height:70px;">
                              {{ item.message }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <!-- End Modals -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Area Chart Ends -->
    </div>
  </div>
</div>
<!-- Begin Page Content Ends -->
<!-- Send Message popup start -->
<app-send-message [listOfFacilities]="listOfFacilities"></app-send-message>
<!-- Send Message popup end -->