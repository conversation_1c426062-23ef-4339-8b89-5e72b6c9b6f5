/* Message Hub Modal Styles - Enhanced for iPhone and Safari compatibility */

/* Mobile-specific styles for Message Hub modals */
@media (max-width: 767.98px) {
    .mobile-message-modal {
        max-width: 95% !important;
        margin: 0.5rem auto;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        top: auto;
        transform: none !important;
    }

    .mobile-message-modal .modal-content {
        border-radius: 0.5rem 0.5rem 0 0;
        max-height: 90vh;
        border-bottom: none;
        overflow: hidden;
    }

    .mobile-message-modal .modal-header {
        padding: 0.5rem 1rem;
        background-color: #0169ab !important;
        color: white !important;
        border-bottom: 1px solid #dee2e6;
    }

    .mobile-message-modal .modal-title {
        font-size: 1rem;
        font-weight: 600;
        color: white !important;
    }

    .mobile-message-modal .modal-body {
        padding: 0.75rem;
        max-height: 70vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .mobile-message-modal .close {
        color: #fff !important;
        opacity: 1 !important;
        text-shadow: none !important;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        background: transparent;
        border: 0;
        cursor: pointer;
    }

    .mobile-message-modal .close:hover,
    .mobile-message-modal .close:focus {
        color: #fff !important;
        opacity: 0.8 !important;
        text-decoration: none !important;
        outline: none;
    }
}

/* iPhone and Safari specific fixes */
@media (max-width: 767px) {
    /* Fix for iOS modal positioning */
    .modal.fade .modal-dialog {
        transform: none !important;
    }

    .modal.show .modal-dialog {
        transform: none !important;
    }

    /* Fix for iOS modal backdrop */
    .modal-backdrop {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        z-index: 1040 !important;
    }

    /* iOS specific modal body fixes */
    .modal-open {
        position: fixed !important;
        width: 100% !important;
        overflow: hidden !important;
    }

    /* Fix for iOS modal scrolling */
    .modal {
        -webkit-overflow-scrolling: touch !important;
        overflow-y: auto !important;
    }
}

/* Safari desktop browser fixes */
@media (min-width: 768px) {
    /* Safari desktop modal backdrop fix */
    .modal-backdrop.show {
        opacity: 0.5 !important;
    }

    /* Ensure close button works properly in Safari */
    .close {
        cursor: pointer !important;
        background: transparent !important;
        border: 0 !important;
    }

    .close:focus {
        outline: none !important;
    }
}


